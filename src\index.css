@tailwind base;
@tailwind components;
@tailwind utilities;

/* Urban Safety Dashboard Design System - Dark Futuristic Theme */

@layer base {
  :root {
    /* Dark Theme - Primary Background & Content */
    --background: 220 27% 8%;
    --foreground: 210 40% 98%;

    /* Glass Cards & Surfaces */
    --card: 220 27% 10%;
    --card-foreground: 210 40% 95%;

    --popover: 220 27% 12%;
    --popover-foreground: 210 40% 95%;

    /* Safety Color System */
    --safe: 141 76% 48%;        /* #00C853 - Green */
    --medium-risk: 36 100% 50%; /* #FF9800 - Orange */
    --high-risk: 4 90% 58%;     /* #E53935 - Red */
    --accent: 207 90% 54%;      /* #2196F3 - Blue */

    /* UI State Colors */
    --primary: 207 90% 54%;
    --primary-foreground: 210 40% 98%;

    --secondary: 220 27% 15%;
    --secondary-foreground: 210 40% 90%;

    --muted: 220 27% 12%;
    --muted-foreground: 215 20% 65%;

    --destructive: 4 90% 58%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 27% 20%;
    --input: 220 27% 15%;
    --ring: 207 90% 54%;

    --radius: 0.75rem;

    /* Glassmorphism Effects */
    --glass-bg: 220 27% 15% / 0.1;
    --glass-border: 210 40% 80% / 0.1;
    --glass-shadow: 0 8px 32px 0 rgb(0 0 0 / 0.37);
    
    /* Glow Effects */
    --glow-safe: 141 76% 48% / 0.3;
    --glow-risk: 4 90% 58% / 0.3;
    --glow-accent: 207 90% 54% / 0.3;

    /* Gradients */
    --gradient-glass: linear-gradient(135deg, hsl(220 27% 15% / 0.1), hsl(220 27% 10% / 0.2));
    --gradient-hero: linear-gradient(135deg, hsl(207 90% 54% / 0.1), hsl(141 76% 48% / 0.1));
    --gradient-danger: linear-gradient(135deg, hsl(4 90% 58% / 0.2), hsl(36 100% 50% / 0.1));

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    background-image: radial-gradient(circle at 20% 80%, hsl(207 90% 54% / 0.05) 0%, transparent 50%),
                      radial-gradient(circle at 80% 20%, hsl(141 76% 48% / 0.05) 0%, transparent 50%);
  }
}

@layer components {
  /* Glass Card Component */
  .glass-card {
    @apply bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-xl border border-white/10 rounded-2xl;
    box-shadow: var(--glass-shadow);
  }
  
  /* Glass Button Variants */
  .glass-button {
    @apply bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm border border-white/20 rounded-xl transition-all duration-300 hover:from-white/20 hover:to-white/10 hover:scale-105;
  }
  
  /* Safety Status Indicators */
  .status-safe {
    @apply bg-gradient-to-r from-green-500/20 to-green-400/10 border border-green-500/30 text-green-400;
    box-shadow: 0 0 20px hsl(var(--glow-safe));
  }
  
  .status-medium {
    @apply bg-gradient-to-r from-orange-500/20 to-orange-400/10 border border-orange-500/30 text-orange-400;
    box-shadow: 0 0 20px hsl(36 100% 50% / 0.3);
  }
  
  .status-danger {
    @apply bg-gradient-to-r from-red-500/20 to-red-400/10 border border-red-500/30 text-red-400;
    box-shadow: 0 0 20px hsl(var(--glow-risk));
  }
  
  /* Animated Glow Effects */
  .glow-accent {
    box-shadow: 0 0 30px hsl(var(--glow-accent));
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }
  
  .glow-safe {
    box-shadow: 0 0 30px hsl(var(--glow-safe));
  }
  
  .glow-danger {
    box-shadow: 0 0 30px hsl(var(--glow-risk));
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-blue-400 to-green-400 bg-clip-text text-transparent;
  }
  
  .border-gradient {
    border-image: linear-gradient(135deg, hsl(207 90% 54% / 0.5), hsl(141 76% 48% / 0.5)) 1;
  }
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 20px hsl(var(--glow-accent));
  }
  100% {
    box-shadow: 0 0 40px hsl(var(--glow-accent)), 0 0 60px hsl(var(--glow-accent));
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}